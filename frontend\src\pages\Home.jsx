import { Helmet } from 'react-helmet-async'
import { motion } from 'framer-motion'
import { <PERSON> } from 'react-router-dom'
import { FaArrowRight, FaMountain, FaCompass, FaCampground } from 'react-icons/fa'

const Home = () => {
  return (
    <>
      <Helmet>
        <title>Wilderness Trails - Your Guide to Backpacking & Wilderness Camping</title>
        <meta 
          name="description" 
          content="Discover the best backpacking and wilderness camping adventures. Expert gear reviews, trail guides, and wilderness skills for outdoor enthusiasts." 
        />
      </Helmet>

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center bg-gradient-nature text-white overflow-hidden">
        <div className="absolute inset-0 bg-black/30" />
        <div className="absolute inset-0 bg-hero-pattern" />
        
        <div className="relative z-10 container-custom text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-5xl md:text-7xl font-heading font-bold mb-6 text-shadow-lg">
              Wilderness Trails
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto text-shadow">
              Your ultimate guide to backpacking and wilderness camping adventures. 
              Discover hidden trails, master outdoor skills, and find your next great adventure.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/blog"
                className="btn-secondary btn-lg inline-flex items-center"
              >
                Explore Adventures
                <FaArrowRight className="ml-2" />
              </Link>
              <Link
                to="/about"
                className="btn-outline btn-lg text-white border-white hover:bg-white hover:text-primary-600"
              >
                Learn More
              </Link>
            </div>
          </motion.div>
        </div>

        {/* Scroll indicator */}
        <motion.div
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <div className="w-6 h-10 border-2 border-white rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white rounded-full mt-2" />
          </div>
        </motion.div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-heading font-bold text-neutral-900 mb-6">
              Everything You Need for Your Next Adventure
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              From gear reviews to trail guides, we provide comprehensive resources 
              for backpackers and wilderness enthusiasts of all levels.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                icon: FaMountain,
                title: 'Trail Guides',
                description: 'Detailed guides to the best backpacking trails, complete with maps, difficulty ratings, and insider tips.'
              },
              {
                icon: FaCompass,
                title: 'Gear Reviews',
                description: 'In-depth reviews of the latest outdoor gear, tested in real wilderness conditions by experienced backpackers.'
              },
              {
                icon: FaCampground,
                title: 'Wilderness Skills',
                description: 'Learn essential outdoor skills from navigation and shelter building to wilderness safety and Leave No Trace principles.'
              }
            ].map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="card text-center"
              >
                <div className="card-body">
                  <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <feature.icon className="w-8 h-8 text-primary-600" />
                  </div>
                  <h3 className="text-2xl font-heading font-semibold mb-4">
                    {feature.title}
                  </h3>
                  <p className="text-neutral-600 leading-relaxed">
                    {feature.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-sunset text-white">
        <div className="container-custom text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-5xl font-heading font-bold mb-6">
              Ready to Start Your Adventure?
            </h2>
            <p className="text-xl mb-8 max-w-2xl mx-auto">
              Join our community of wilderness enthusiasts and never miss an adventure. 
              Get the latest trail guides, gear reviews, and outdoor tips.
            </p>
            <Link
              to="/blog"
              className="btn-secondary btn-lg bg-white text-secondary-600 hover:bg-neutral-100"
            >
              Start Exploring
            </Link>
          </motion.div>
        </div>
      </section>
    </>
  )
}

export default Home
