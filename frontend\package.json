{"name": "wilderness-trails-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "axios": "^1.6.2", "react-query": "^3.39.3", "react-hook-form": "^7.48.2", "react-icons": "^4.12.0", "framer-motion": "^10.16.16", "react-helmet-async": "^1.3.0", "react-share": "^4.4.1", "date-fns": "^2.30.0", "clsx": "^2.0.0", "tailwindcss": "^3.3.6", "react-hot-toast": "^2.4.1"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.1", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "vite": "^5.0.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@tailwindcss/typography": "^0.5.10", "@tailwindcss/forms": "^0.5.7"}}