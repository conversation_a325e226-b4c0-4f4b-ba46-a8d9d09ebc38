import { Helmet } from 'react-helmet-async'
import { motion } from 'framer-motion'

const About = () => {
  return (
    <>
      <Helmet>
        <title>About - Wilderness Trails</title>
        <meta 
          name="description" 
          content="Learn about Wilderness Trails and our mission to help outdoor enthusiasts explore the wilderness safely and responsibly." 
        />
      </Helmet>

      <div className="min-h-screen bg-neutral-50 pt-20">
        <section className="py-16">
          <div className="container-custom">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="max-w-4xl mx-auto text-center"
            >
              <h1 className="text-4xl md:text-5xl font-heading font-bold text-neutral-900 mb-6">
                About Wilderness Trails
              </h1>
              <p className="text-xl text-neutral-600 leading-relaxed">
                We're passionate about helping outdoor enthusiasts discover the beauty and adventure 
                of wilderness backpacking and camping. Our mission is to provide comprehensive guides, 
                honest gear reviews, and essential skills to help you explore safely and responsibly.
              </p>
            </motion.div>
          </div>
        </section>
      </div>
    </>
  )
}

export default About
