import { Helmet } from 'react-helmet-async'
import { <PERSON> } from 'react-router-dom'
import { motion } from 'framer-motion'
import { FaHome, FaMountain } from 'react-icons/fa'

const NotFound = () => {
  return (
    <>
      <Helmet>
        <title>Page Not Found - Wilderness Trails</title>
      </Helmet>
      <div className="min-h-screen bg-neutral-50 pt-20 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center"
        >
          <FaMountain className="w-24 h-24 text-primary-400 mx-auto mb-6" />
          <h1 className="text-6xl font-heading font-bold text-neutral-900 mb-4">
            404
          </h1>
          <h2 className="text-2xl font-heading font-semibold text-neutral-700 mb-4">
            Trail Not Found
          </h2>
          <p className="text-neutral-600 mb-8 max-w-md mx-auto">
            Looks like you've wandered off the beaten path. Let's get you back to familiar territory.
          </p>
          <Link
            to="/"
            className="btn-primary inline-flex items-center"
          >
            <FaHome className="mr-2" />
            Return Home
          </Link>
        </motion.div>
      </div>
    </>
  )
}

export default NotFound
