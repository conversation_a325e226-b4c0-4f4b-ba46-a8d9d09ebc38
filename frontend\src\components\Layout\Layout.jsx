import { useState, useEffect } from 'react'
import Header from './Header'
import Footer from './Footer'
import ScrollToTop from '../UI/ScrollToTop'
import LoadingSpinner from '../UI/LoadingSpinner'
import { useAuth } from '../../contexts/AuthContext'

const Layout = ({ children }) => {
  const [isLoading, setIsLoading] = useState(true)
  const { isLoading: authLoading } = useAuth()

  useEffect(() => {
    // Simulate initial loading
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  if (isLoading || authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-neutral-50">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="min-h-screen flex flex-col bg-neutral-50">
      <Header />
      <main className="flex-grow">
        {children}
      </main>
      <Footer />
      <ScrollToTop />
    </div>
  )
}

export default Layout
