@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom base styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply font-body text-neutral-800 bg-neutral-50;
    line-height: 1.6;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-heading font-semibold text-neutral-900;
    line-height: 1.2;
  }
  
  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl;
  }
  
  h2 {
    @apply text-3xl md:text-4xl lg:text-5xl;
  }
  
  h3 {
    @apply text-2xl md:text-3xl lg:text-4xl;
  }
  
  h4 {
    @apply text-xl md:text-2xl lg:text-3xl;
  }
  
  h5 {
    @apply text-lg md:text-xl lg:text-2xl;
  }
  
  h6 {
    @apply text-base md:text-lg lg:text-xl;
  }
  
  p {
    @apply mb-4 leading-relaxed;
  }
  
  a {
    @apply text-primary-600 hover:text-primary-700 transition-colors duration-200;
  }
  
  img {
    @apply max-w-full h-auto;
  }
}

/* Custom component styles */
@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
  }
  
  .btn-outline {
    @apply btn border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white focus:ring-primary-500;
  }
  
  .btn-ghost {
    @apply btn text-neutral-600 hover:text-neutral-900 hover:bg-neutral-100 focus:ring-neutral-500;
  }
  
  .btn-lg {
    @apply px-6 py-3 text-base;
  }
  
  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-md overflow-hidden transition-shadow duration-200 hover:shadow-lg;
  }
  
  .card-body {
    @apply p-6;
  }
  
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply badge bg-primary-100 text-primary-800;
  }
  
  .badge-secondary {
    @apply badge bg-secondary-100 text-secondary-800;
  }
  
  .form-input {
    @apply block w-full px-3 py-2 border border-neutral-300 rounded-md shadow-sm placeholder-neutral-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }
  
  .form-textarea {
    @apply form-input resize-vertical;
  }
  
  .form-select {
    @apply form-input pr-10 bg-white;
  }
  
  .form-label {
    @apply block text-sm font-medium text-neutral-700 mb-1;
  }
  
  .form-error {
    @apply text-red-600 text-sm mt-1;
  }
  
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .prose-custom {
    @apply prose prose-lg max-w-none;
  }
  
  .prose-custom h1,
  .prose-custom h2,
  .prose-custom h3,
  .prose-custom h4,
  .prose-custom h5,
  .prose-custom h6 {
    @apply font-heading text-neutral-900;
  }
  
  .prose-custom a {
    @apply text-primary-600 hover:text-primary-700 no-underline;
  }
  
  .prose-custom blockquote {
    @apply border-l-4 border-primary-500 bg-primary-50 pl-4 py-2 my-6 italic;
  }
  
  .prose-custom code {
    @apply bg-neutral-100 text-neutral-800 px-1 py-0.5 rounded text-sm;
  }
  
  .prose-custom pre {
    @apply bg-neutral-900 text-neutral-100 p-4 rounded-lg overflow-x-auto;
  }
  
  .prose-custom pre code {
    @apply bg-transparent text-neutral-100 p-0;
  }
}

/* Custom utility styles */
@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
  
  .bg-gradient-nature {
    background: linear-gradient(135deg, #349e63 0%, #26804f 100%);
  }
  
  .bg-gradient-sunset {
    background: linear-gradient(135deg, #d9844a 0%, #cb6d3f 100%);
  }
  
  .bg-hero-pattern {
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23349e63' fill-opacity='0.05'%3E%3Cpath d='m36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  }
  
  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
  }
  
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .animate-pulse-gentle {
    animation: pulseGentle 2s infinite;
  }
  
  @keyframes pulseGentle {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.8;
    }
  }
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-neutral-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-neutral-400 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-neutral-500;
}

/* Selection styling */
::selection {
  @apply bg-primary-200 text-primary-900;
}

/* Focus styles for accessibility */
.focus-visible {
  @apply outline-none ring-2 ring-primary-500 ring-offset-2;
}
