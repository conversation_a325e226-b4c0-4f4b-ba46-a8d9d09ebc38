import { useState, useEffect } from 'react'
import { Helmet } from 'react-helmet-async'
import { useQuery } from 'react-query'
import { motion } from 'framer-motion'
import { FaSearch, Fa<PERSON>ilter, FaCalendar, Fa<PERSON>ser, <PERSON>a<PERSON>ye, FaHeart } from 'react-icons/fa'
import { blogAPI, categoryAPI } from '../services/api'
import LoadingSpinner from '../components/UI/LoadingSpinner'
import { Link } from 'react-router-dom'
import { format } from 'date-fns'

const BlogList = () => {
  const [currentPage, setCurrentPage] = useState(1)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [sortBy, setSortBy] = useState('-publishedAt')

  // Fetch posts
  const { data: postsData, isLoading: postsLoading, error: postsError } = useQuery(
    ['posts', currentPage, searchQuery, selectedCategory, sortBy],
    () => blogAPI.getAllPosts({
      page: currentPage,
      limit: 12,
      search: searchQuery,
      category: selectedCategory,
      sort: sortBy
    }),
    {
      keepPreviousData: true
    }
  )

  // Fetch categories
  const { data: categoriesData } = useQuery('categories', categoryAPI.getAllCategories)

  const posts = postsData?.data?.data || []
  const pagination = postsData?.data?.pagination || {}
  const categories = categoriesData?.data?.data || []

  const handleSearch = (e) => {
    e.preventDefault()
    setCurrentPage(1)
  }

  const handleCategoryChange = (categoryId) => {
    setSelectedCategory(categoryId)
    setCurrentPage(1)
  }

  const handleSortChange = (sort) => {
    setSortBy(sort)
    setCurrentPage(1)
  }

  if (postsLoading && !posts.length) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (postsError) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-heading font-semibold text-neutral-900 mb-2">
            Something went wrong
          </h2>
          <p className="text-neutral-600">Unable to load blog posts. Please try again later.</p>
        </div>
      </div>
    )
  }

  return (
    <>
      <Helmet>
        <title>Blog - Wilderness Trails</title>
        <meta 
          name="description" 
          content="Explore our latest backpacking and wilderness camping blog posts. Find gear reviews, trail guides, and outdoor adventure stories." 
        />
      </Helmet>

      <div className="min-h-screen bg-neutral-50 pt-20">
        {/* Header */}
        <section className="bg-gradient-nature text-white py-16">
          <div className="container-custom text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h1 className="text-4xl md:text-5xl font-heading font-bold mb-4">
                Adventure Blog
              </h1>
              <p className="text-xl max-w-2xl mx-auto">
                Discover the latest stories, guides, and insights from the wilderness
              </p>
            </motion.div>
          </div>
        </section>

        {/* Filters */}
        <section className="py-8 bg-white border-b border-neutral-200">
          <div className="container-custom">
            <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
              {/* Search */}
              <form onSubmit={handleSearch} className="flex-1 max-w-md">
                <div className="relative">
                  <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400" />
                  <input
                    type="text"
                    placeholder="Search posts..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>
              </form>

              {/* Filters */}
              <div className="flex flex-wrap gap-4 items-center">
                {/* Category Filter */}
                <select
                  value={selectedCategory}
                  onChange={(e) => handleCategoryChange(e.target.value)}
                  className="px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="">All Categories</option>
                  {categories.map((category) => (
                    <option key={category._id} value={category._id}>
                      {category.name}
                    </option>
                  ))}
                </select>

                {/* Sort Filter */}
                <select
                  value={sortBy}
                  onChange={(e) => handleSortChange(e.target.value)}
                  className="px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="-publishedAt">Latest First</option>
                  <option value="publishedAt">Oldest First</option>
                  <option value="-views">Most Viewed</option>
                  <option value="title">Alphabetical</option>
                </select>
              </div>
            </div>
          </div>
        </section>

        {/* Posts Grid */}
        <section className="py-12">
          <div className="container-custom">
            {posts.length === 0 ? (
              <div className="text-center py-12">
                <h3 className="text-2xl font-heading font-semibold text-neutral-900 mb-2">
                  No posts found
                </h3>
                <p className="text-neutral-600">
                  Try adjusting your search or filter criteria.
                </p>
              </div>
            ) : (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {posts.map((post, index) => (
                    <motion.article
                      key={post._id}
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: index * 0.1 }}
                      className="card group"
                    >
                      {/* Featured Image */}
                      <div className="relative overflow-hidden">
                        <img
                          src={post.featuredImage || '/default-post-image.jpg'}
                          alt={post.imageAlt || post.title}
                          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                        {post.featured && (
                          <div className="absolute top-4 left-4">
                            <span className="badge-primary">Featured</span>
                          </div>
                        )}
                        <div className="absolute top-4 right-4">
                          <span 
                            className="badge text-white"
                            style={{ backgroundColor: post.category?.color || '#349e63' }}
                          >
                            {post.category?.name}
                          </span>
                        </div>
                      </div>

                      {/* Content */}
                      <div className="card-body">
                        <h2 className="text-xl font-heading font-semibold mb-2 group-hover:text-primary-600 transition-colors">
                          <Link to={`/blog/${post.slug}`}>
                            {post.title}
                          </Link>
                        </h2>
                        
                        <p className="text-neutral-600 mb-4 line-clamp-3">
                          {post.excerpt}
                        </p>

                        {/* Meta */}
                        <div className="flex items-center justify-between text-sm text-neutral-500">
                          <div className="flex items-center space-x-4">
                            <div className="flex items-center space-x-1">
                              <FaUser className="w-3 h-3" />
                              <span>{post.author?.name}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <FaCalendar className="w-3 h-3" />
                              <span>{format(new Date(post.publishedAt), 'MMM d, yyyy')}</span>
                            </div>
                          </div>
                          
                          <div className="flex items-center space-x-3">
                            <div className="flex items-center space-x-1">
                              <FaEye className="w-3 h-3" />
                              <span>{post.views}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <FaHeart className="w-3 h-3" />
                              <span>{post.likes?.length || 0}</span>
                            </div>
                          </div>
                        </div>

                        {/* Tags */}
                        {post.tags && post.tags.length > 0 && (
                          <div className="mt-4 flex flex-wrap gap-2">
                            {post.tags.slice(0, 3).map((tag) => (
                              <span
                                key={tag}
                                className="text-xs bg-neutral-100 text-neutral-600 px-2 py-1 rounded"
                              >
                                #{tag}
                              </span>
                            ))}
                          </div>
                        )}
                      </div>
                    </motion.article>
                  ))}
                </div>

                {/* Pagination */}
                {pagination.pages > 1 && (
                  <div className="mt-12 flex justify-center">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => setCurrentPage(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="px-4 py-2 border border-neutral-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-neutral-50"
                      >
                        Previous
                      </button>
                      
                      {Array.from({ length: pagination.pages }, (_, i) => i + 1).map((page) => (
                        <button
                          key={page}
                          onClick={() => setCurrentPage(page)}
                          className={`px-4 py-2 border rounded-lg ${
                            currentPage === page
                              ? 'bg-primary-600 text-white border-primary-600'
                              : 'border-neutral-300 hover:bg-neutral-50'
                          }`}
                        >
                          {page}
                        </button>
                      ))}
                      
                      <button
                        onClick={() => setCurrentPage(currentPage + 1)}
                        disabled={currentPage === pagination.pages}
                        className="px-4 py-2 border border-neutral-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-neutral-50"
                      >
                        Next
                      </button>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        </section>
      </div>
    </>
  )
}

export default BlogList
