import { Helmet } from 'react-helmet-async'
import { motion } from 'framer-motion'

const Contact = () => {
  return (
    <>
      <Helmet>
        <title>Contact - Wilderness Trails</title>
        <meta 
          name="description" 
          content="Get in touch with the Wilderness Trails team. We'd love to hear from you!" 
        />
      </Helmet>

      <div className="min-h-screen bg-neutral-50 pt-20">
        <section className="py-16">
          <div className="container-custom">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="max-w-4xl mx-auto text-center"
            >
              <h1 className="text-4xl md:text-5xl font-heading font-bold text-neutral-900 mb-6">
                Contact Us
              </h1>
              <p className="text-xl text-neutral-600 leading-relaxed">
                Have a question about a trail, gear recommendation, or want to share your adventure story? 
                We'd love to hear from you!
              </p>
            </motion.div>
          </div>
        </section>
      </div>
    </>
  )
}

export default Contact
