const express = require('express');
const { body } = require('express-validator');
const {
  getAllPosts,
  getPost,
  createPost,
  updatePost,
  deletePost,
  getFeaturedPosts,
  getPostsByCategory,
  getPostsByTag,
  searchPosts,
  likePost,
  incrementViews
} = require('../controllers/blogController');
const { protect, admin, optionalAuth } = require('../middleware/auth');

const router = express.Router();

// Validation rules
const createPostValidation = [
  body('title')
    .trim()
    .isLength({ min: 5, max: 100 })
    .withMessage('Title must be between 5 and 100 characters'),
  body('excerpt')
    .trim()
    .isLength({ min: 10, max: 300 })
    .withMessage('Excerpt must be between 10 and 300 characters'),
  body('content')
    .trim()
    .isLength({ min: 50 })
    .withMessage('Content must be at least 50 characters'),
  body('category')
    .isMongoId()
    .withMessage('Please provide a valid category ID'),
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array'),
  body('status')
    .optional()
    .isIn(['draft', 'published', 'archived'])
    .withMessage('Status must be draft, published, or archived')
];

const updatePostValidation = [
  body('title')
    .optional()
    .trim()
    .isLength({ min: 5, max: 100 })
    .withMessage('Title must be between 5 and 100 characters'),
  body('excerpt')
    .optional()
    .trim()
    .isLength({ min: 10, max: 300 })
    .withMessage('Excerpt must be between 10 and 300 characters'),
  body('content')
    .optional()
    .trim()
    .isLength({ min: 50 })
    .withMessage('Content must be at least 50 characters'),
  body('category')
    .optional()
    .isMongoId()
    .withMessage('Please provide a valid category ID'),
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array'),
  body('status')
    .optional()
    .isIn(['draft', 'published', 'archived'])
    .withMessage('Status must be draft, published, or archived')
];

// Public routes
router.get('/', getAllPosts);
router.get('/featured', getFeaturedPosts);
router.get('/search', searchPosts);
router.get('/category/:categorySlug', getPostsByCategory);
router.get('/tag/:tag', getPostsByTag);
router.get('/:slug', optionalAuth, getPost);
router.put('/:id/views', incrementViews);

// Protected routes
router.post('/:id/like', protect, likePost);

// Admin routes
router.post('/', protect, admin, createPostValidation, createPost);
router.put('/:id', protect, admin, updatePostValidation, updatePost);
router.delete('/:id', protect, admin, deletePost);

module.exports = router;
