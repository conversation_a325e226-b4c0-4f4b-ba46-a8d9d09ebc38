<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/tent-icon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Wilderness Trails - Your guide to backpacking and wilderness camping adventures. Discover gear reviews, trail guides, and wilderness skills." />
    <meta name="keywords" content="camping, backpacking, wilderness, hiking, outdoor gear, trail guides" />
    <meta name="author" content="Wilderness Trails Blog" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://wilderness-trails.com/" />
    <meta property="og:title" content="Wilderness Trails - Backpacking & Wilderness Camping Blog" />
    <meta property="og:description" content="Your guide to backpacking and wilderness camping adventures. Discover gear reviews, trail guides, and wilderness skills." />
    <meta property="og:image" content="/og-image.jpg" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://wilderness-trails.com/" />
    <meta property="twitter:title" content="Wilderness Trails - Backpacking & Wilderness Camping Blog" />
    <meta property="twitter:description" content="Your guide to backpacking and wilderness camping adventures. Discover gear reviews, trail guides, and wilderness skills." />
    <meta property="twitter:image" content="/og-image.jpg" />

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Merriweather:ital,wght@0,300;0,400;0,700;0,900;1,300;1,400;1,700;1,900&family=Montserrat:ital,wght@0,100..900;1,100..900&family=Inter:wght@100..900&display=swap" rel="stylesheet">
    
    <title>Wilderness Trails - Backpacking & Wilderness Camping Blog</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
