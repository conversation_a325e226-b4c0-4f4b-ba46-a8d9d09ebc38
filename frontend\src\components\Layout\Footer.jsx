import { Link } from 'react-router-dom'
import { 
  FaMountain, 
  FaFacebook, 
  FaTwitter, 
  FaInstagram, 
  FaYoutube,
  FaHeart,
  FaEnvelope,
  FaPhone,
  FaMapMarkerAlt
} from 'react-icons/fa'

const Footer = () => {
  const currentYear = new Date().getFullYear()

  const footerLinks = {
    explore: [
      { name: 'Latest Posts', href: '/blog' },
      { name: 'Gear Reviews', href: '/category/gear-reviews' },
      { name: 'Trail Guides', href: '/category/trail-guides' },
      { name: 'Wilderness Skills', href: '/category/wilderness-skills' }
    ],
    company: [
      { name: 'About Us', href: '/about' },
      { name: 'Contact', href: '/contact' },
      { name: 'Privacy Policy', href: '/privacy' },
      { name: 'Terms of Service', href: '/terms' }
    ],
    resources: [
      { name: '<PERSON><PERSON><PERSON>\'s Guide', href: '/beginners-guide' },
      { name: 'Safety Tips', href: '/safety-tips' },
      { name: 'Leave No Trace', href: '/leave-no-trace' },
      { name: 'Emergency Contacts', href: '/emergency' }
    ]
  }

  const socialLinks = [
    { name: 'Facebook', icon: FaFacebook, href: '#', color: 'hover:text-blue-600' },
    { name: 'Twitter', icon: FaTwitter, href: '#', color: 'hover:text-blue-400' },
    { name: 'Instagram', icon: FaInstagram, href: '#', color: 'hover:text-pink-600' },
    { name: 'YouTube', icon: FaYoutube, href: '#', color: 'hover:text-red-600' }
  ]

  return (
    <footer className="bg-neutral-900 text-neutral-300">
      {/* Main Footer */}
      <div className="container-custom py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-1">
            <Link to="/" className="flex items-center space-x-2 text-white mb-4">
              <FaMountain className="h-8 w-8 text-primary-400" />
              <span className="font-heading font-bold text-xl">
                Wilderness Trails
              </span>
            </Link>
            <p className="text-neutral-400 mb-6 leading-relaxed">
              Your trusted companion for backpacking and wilderness adventures. 
              Discover trails, master skills, and explore the great outdoors safely.
            </p>
            
            {/* Contact Info */}
            <div className="space-y-2 text-sm">
              <div className="flex items-center space-x-2">
                <FaEnvelope className="w-4 h-4 text-primary-400" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center space-x-2">
                <FaPhone className="w-4 h-4 text-primary-400" />
                <span>+****************</span>
              </div>
              <div className="flex items-center space-x-2">
                <FaMapMarkerAlt className="w-4 h-4 text-primary-400" />
                <span>Pacific Northwest, USA</span>
              </div>
            </div>
          </div>

          {/* Explore Links */}
          <div>
            <h3 className="font-heading font-semibold text-white mb-4">Explore</h3>
            <ul className="space-y-2">
              {footerLinks.explore.map((link) => (
                <li key={link.name}>
                  <Link
                    to={link.href}
                    className="text-neutral-400 hover:text-primary-400 transition-colors duration-200"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Company Links */}
          <div>
            <h3 className="font-heading font-semibold text-white mb-4">Company</h3>
            <ul className="space-y-2">
              {footerLinks.company.map((link) => (
                <li key={link.name}>
                  <Link
                    to={link.href}
                    className="text-neutral-400 hover:text-primary-400 transition-colors duration-200"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Resources Links */}
          <div>
            <h3 className="font-heading font-semibold text-white mb-4">Resources</h3>
            <ul className="space-y-2">
              {footerLinks.resources.map((link) => (
                <li key={link.name}>
                  <Link
                    to={link.href}
                    className="text-neutral-400 hover:text-primary-400 transition-colors duration-200"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Newsletter Signup */}
        <div className="mt-12 pt-8 border-t border-neutral-800">
          <div className="max-w-md mx-auto text-center">
            <h3 className="font-heading font-semibold text-white mb-2">
              Stay Connected
            </h3>
            <p className="text-neutral-400 mb-4">
              Get the latest trail guides and outdoor tips delivered to your inbox.
            </p>
            <div className="flex">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-2 bg-neutral-800 border border-neutral-700 rounded-l-md text-white placeholder-neutral-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
              <button className="px-6 py-2 bg-primary-600 text-white rounded-r-md hover:bg-primary-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-neutral-900">
                Subscribe
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="border-t border-neutral-800">
        <div className="container-custom py-6">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            {/* Copyright */}
            <div className="flex items-center space-x-1 text-sm text-neutral-400">
              <span>© {currentYear} Wilderness Trails. Made with</span>
              <FaHeart className="w-4 h-4 text-red-500" />
              <span>for outdoor enthusiasts.</span>
            </div>

            {/* Social Links */}
            <div className="flex items-center space-x-4">
              {socialLinks.map((social) => (
                <a
                  key={social.name}
                  href={social.href}
                  className={`text-neutral-400 ${social.color} transition-colors duration-200`}
                  aria-label={social.name}
                >
                  <social.icon className="w-5 h-5" />
                </a>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
