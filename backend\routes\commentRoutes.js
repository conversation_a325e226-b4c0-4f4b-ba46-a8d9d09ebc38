const express = require('express');
const { body } = require('express-validator');
const {
  getCommentsByPost,
  createComment,
  updateComment,
  deleteComment,
  approveComment,
  rejectComment,
  likeComment
} = require('../controllers/commentController');
const { protect, admin, optionalAuth } = require('../middleware/auth');

const router = express.Router();

// Validation rules
const createCommentValidation = [
  body('content')
    .trim()
    .isLength({ min: 5, max: 1000 })
    .withMessage('Comment must be between 5 and 1000 characters'),
  body('author.name')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Name must be between 2 and 50 characters'),
  body('author.email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('author.website')
    .optional()
    .isURL()
    .withMessage('Please provide a valid URL'),
  body('post')
    .isMongoId()
    .withMessage('Please provide a valid post ID'),
  body('parentComment')
    .optional()
    .isMongoId()
    .withMessage('Please provide a valid parent comment ID')
];

const updateCommentValidation = [
  body('content')
    .optional()
    .trim()
    .isLength({ min: 5, max: 1000 })
    .withMessage('Comment must be between 5 and 1000 characters'),
  body('status')
    .optional()
    .isIn(['pending', 'approved', 'spam', 'rejected'])
    .withMessage('Status must be pending, approved, spam, or rejected')
];

// Public routes
router.get('/post/:postId', getCommentsByPost);
router.post('/', createCommentValidation, createComment);

// Protected routes
router.post('/:id/like', protect, likeComment);
router.put('/:id', protect, updateCommentValidation, updateComment);
router.delete('/:id', protect, deleteComment);

// Admin routes
router.put('/:id/approve', protect, admin, approveComment);
router.put('/:id/reject', protect, admin, rejectComment);

module.exports = router;
