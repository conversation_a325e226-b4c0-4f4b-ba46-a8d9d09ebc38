const http = require('http');
const url = require('url');
const querystring = require('querystring');

console.log('🚀 Starting Wilderness Trails Backend API Server...');

// Mock data for the camping blog
const mockData = {
  categories: [
    { 
      _id: '1', 
      name: 'Gear Reviews', 
      slug: 'gear-reviews', 
      color: '#349e63',
      description: 'In-depth reviews of backpacking and camping gear',
      postCount: 12
    },
    { 
      _id: '2', 
      name: 'Trail Guides', 
      slug: 'trail-guides', 
      color: '#d9844a',
      description: 'Detailed guides to the best hiking and camping trails',
      postCount: 8
    },
    { 
      _id: '3', 
      name: 'Wilderness Skills', 
      slug: 'wilderness-skills', 
      color: '#a08862',
      description: 'Essential outdoor skills and survival techniques',
      postCount: 15
    },
    { 
      _id: '4', 
      name: 'Trip Reports', 
      slug: 'trip-reports', 
      color: '#6b6b68',
      description: 'Personal adventure stories and experiences',
      postCount: 6
    }
  ],
  
  blogPosts: [
    {
      _id: '1',
      title: 'Essential Backpacking Gear for Beginners',
      slug: 'essential-backpacking-gear-beginners',
      excerpt: 'A comprehensive guide to the must-have gear for your first backpacking adventure. Learn what to pack and what to leave behind.',
      content: 'Starting your backpacking journey can be overwhelming with all the gear options available. This guide breaks down the essential items you need for a safe and enjoyable wilderness experience...',
      featuredImage: '/images/backpacking-gear.jpg',
      author: { name: 'Sarah <PERSON>', avatar: '/images/sarah.jpg' },
      category: { _id: '1', name: 'Gear Reviews', slug: 'gear-reviews', color: '#349e63' },
      tags: ['backpacking', 'gear', 'beginners', 'essentials'],
      status: 'published',
      featured: true,
      readTime: 8,
      views: 1247,
      likes: 89,
      publishedAt: '2024-12-15T10:00:00Z',
      createdAt: '2024-12-15T10:00:00Z'
    },
    {
      _id: '2',
      title: 'Top 10 Wilderness Trails in the Pacific Northwest',
      slug: 'top-10-wilderness-trails-pacific-northwest',
      excerpt: 'Discover the most breathtaking wilderness trails in Washington and Oregon. From alpine lakes to old-growth forests.',
      content: 'The Pacific Northwest offers some of the most spectacular wilderness experiences in North America. Here are our top 10 trails that showcase the region\'s diverse landscapes...',
      featuredImage: '/images/pnw-trails.jpg',
      author: { name: 'Mike Chen', avatar: '/images/mike.jpg' },
      category: { _id: '2', name: 'Trail Guides', slug: 'trail-guides', color: '#d9844a' },
      tags: ['trails', 'pacific-northwest', 'hiking', 'wilderness'],
      status: 'published',
      featured: true,
      readTime: 12,
      views: 2156,
      likes: 156,
      publishedAt: '2024-12-10T14:30:00Z',
      createdAt: '2024-12-10T14:30:00Z'
    },
    {
      _id: '3',
      title: 'Building a Shelter in Emergency Situations',
      slug: 'building-shelter-emergency-situations',
      excerpt: 'Learn essential shelter-building techniques that could save your life in wilderness emergencies.',
      content: 'When you\'re caught in an unexpected situation in the wilderness, knowing how to build an emergency shelter can be the difference between life and death...',
      featuredImage: '/images/emergency-shelter.jpg',
      author: { name: 'David Rodriguez', avatar: '/images/david.jpg' },
      category: { _id: '3', name: 'Wilderness Skills', slug: 'wilderness-skills', color: '#a08862' },
      tags: ['survival', 'shelter', 'emergency', 'skills'],
      status: 'published',
      featured: false,
      readTime: 6,
      views: 892,
      likes: 67,
      publishedAt: '2024-12-08T09:15:00Z',
      createdAt: '2024-12-08T09:15:00Z'
    },
    {
      _id: '4',
      title: 'Solo Backpacking the John Muir Trail',
      slug: 'solo-backpacking-john-muir-trail',
      excerpt: 'A personal account of hiking the iconic 211-mile John Muir Trail solo, including challenges, highlights, and lessons learned.',
      content: 'The John Muir Trail has been on my bucket list for years. This summer, I finally took on the challenge of hiking it solo...',
      featuredImage: '/images/jmt-solo.jpg',
      author: { name: 'Emily Watson', avatar: '/images/emily.jpg' },
      category: { _id: '4', name: 'Trip Reports', slug: 'trip-reports', color: '#6b6b68' },
      tags: ['john-muir-trail', 'solo-hiking', 'california', 'trip-report'],
      status: 'published',
      featured: false,
      readTime: 15,
      views: 1834,
      likes: 203,
      publishedAt: '2024-12-05T16:45:00Z',
      createdAt: '2024-12-05T16:45:00Z'
    }
  ]
};

// Helper function to send JSON response
function sendJSON(res, statusCode, data) {
  res.writeHead(statusCode, {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
  });
  res.end(JSON.stringify(data, null, 2));
}

// Helper function to handle CORS preflight
function handleCORS(res) {
  res.writeHead(200, {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
  });
  res.end();
}

const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const query = parsedUrl.query;
  const method = req.method;

  console.log(`📡 ${method} ${path}`);

  // Handle CORS preflight requests
  if (method === 'OPTIONS') {
    return handleCORS(res);
  }

  // API Routes
  if (path === '/api/health') {
    return sendJSON(res, 200, {
      success: true,
      message: 'Wilderness Trails Backend API is running!',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      endpoints: [
        '/api/health',
        '/api/blogs',
        '/api/categories',
        '/api/blogs/featured'
      ]
    });
  }

  if (path === '/api/categories') {
    return sendJSON(res, 200, {
      success: true,
      count: mockData.categories.length,
      data: mockData.categories
    });
  }

  if (path === '/api/blogs') {
    const page = parseInt(query.page) || 1;
    const limit = parseInt(query.limit) || 10;
    const category = query.category;
    
    let filteredPosts = mockData.blogPosts;
    
    // Filter by category if specified
    if (category) {
      filteredPosts = mockData.blogPosts.filter(post => 
        post.category._id === category || post.category.slug === category
      );
    }
    
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedPosts = filteredPosts.slice(startIndex, endIndex);
    
    return sendJSON(res, 200, {
      success: true,
      count: paginatedPosts.length,
      total: filteredPosts.length,
      pagination: {
        page,
        limit,
        total: filteredPosts.length,
        pages: Math.ceil(filteredPosts.length / limit)
      },
      data: paginatedPosts
    });
  }

  if (path === '/api/blogs/featured') {
    const featuredPosts = mockData.blogPosts.filter(post => post.featured);
    return sendJSON(res, 200, {
      success: true,
      count: featuredPosts.length,
      data: featuredPosts
    });
  }

  if (path.startsWith('/api/blogs/') && path !== '/api/blogs/featured') {
    const slug = path.split('/api/blogs/')[1];
    const post = mockData.blogPosts.find(p => p.slug === slug || p._id === slug);
    
    if (post) {
      return sendJSON(res, 200, {
        success: true,
        data: post
      });
    } else {
      return sendJSON(res, 404, {
        success: false,
        error: 'Blog post not found'
      });
    }
  }

  if (path.startsWith('/api/categories/')) {
    const slug = path.split('/api/categories/')[1];
    const category = mockData.categories.find(c => c.slug === slug || c._id === slug);
    
    if (category) {
      const categoryPosts = mockData.blogPosts.filter(post => 
        post.category._id === category._id
      );
      
      return sendJSON(res, 200, {
        success: true,
        category,
        count: categoryPosts.length,
        data: categoryPosts
      });
    } else {
      return sendJSON(res, 404, {
        success: false,
        error: 'Category not found'
      });
    }
  }

  // Default route
  if (path === '/') {
    return sendJSON(res, 200, {
      message: 'Wilderness Trails Backend API',
      version: '1.0.0',
      endpoints: {
        health: '/api/health',
        categories: '/api/categories',
        blogs: '/api/blogs',
        featured: '/api/blogs/featured',
        singleBlog: '/api/blogs/{slug}',
        categoryPosts: '/api/categories/{slug}'
      }
    });
  }

  // 404 for unknown routes
  return sendJSON(res, 404, {
    success: false,
    error: `Route ${path} not found`
  });
});

const PORT = 8082;

server.listen(PORT, 'localhost', () => {
  console.log(`
🏕️  WILDERNESS TRAILS BACKEND API
✅ Server successfully started!
🌐 URL: http://localhost:${PORT}
📚 Health: http://localhost:${PORT}/api/health
📝 Blogs: http://localhost:${PORT}/api/blogs
🏷️  Categories: http://localhost:${PORT}/api/categories
⭐ Featured: http://localhost:${PORT}/api/blogs/featured
⏰ Started at: ${new Date().toISOString()}
  `);
});

server.on('error', (err) => {
  console.error('❌ Backend API server error:', err);
});
