const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const morgan = require('morgan');
require('dotenv').config();

const app = express();

// Security middleware
app.use(helmet());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
});
app.use('/api/', limiter);

// CORS configuration
app.use(cors({
  origin: process.env.CLIENT_URL || 'http://localhost:5173',
  credentials: true,
}));

// Compression middleware
app.use(compression());

// Logging middleware
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
}

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Static files
app.use('/uploads', express.static('public/uploads'));

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Wilderness Trails API is running (without database)',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
  });
});

// Mock API routes for testing
app.get('/api/blogs', (req, res) => {
  res.json({
    success: true,
    count: 0,
    data: [],
    message: 'Database not connected - mock response'
  });
});

app.get('/api/categories', (req, res) => {
  res.json({
    success: true,
    count: 3,
    data: [
      { _id: '1', name: 'Gear Reviews', slug: 'gear-reviews', color: '#349e63' },
      { _id: '2', name: 'Trail Guides', slug: 'trail-guides', color: '#d9844a' },
      { _id: '3', name: 'Wilderness Skills', slug: 'wilderness-skills', color: '#a08862' }
    ]
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: `Route ${req.originalUrl} not found`
  });
});

// Error handler
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    error: 'Something went wrong!'
  });
});

const PORT = process.env.PORT || 5000;

const server = app.listen(PORT, () => {
  console.log(`
🏕️  Wilderness Trails API Server (No Database Mode)
🚀 Server running in ${process.env.NODE_ENV} mode on port ${PORT}
🌐 API URL: http://localhost:${PORT}/api
📚 Health Check: http://localhost:${PORT}/api/health
  `);
});

module.exports = app;
