const { validationResult } = require('express-validator');
const BlogPost = require('../models/BlogPost');
const Category = require('../models/Category');

// @desc    Get all blog posts
// @route   GET /api/blogs
// @access  Public
const getAllPosts = async (req, res, next) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const status = req.query.status || 'published';
    const category = req.query.category;
    const featured = req.query.featured;
    const sort = req.query.sort || '-publishedAt';

    // Build query
    let query = { status };
    if (category) query.category = category;
    if (featured) query.featured = featured === 'true';

    const posts = await BlogPost.find(query)
      .populate('author', 'name avatar')
      .populate('category', 'name slug color')
      .sort(sort)
      .skip(skip)
      .limit(limit);

    const total = await BlogPost.countDocuments(query);

    res.status(200).json({
      success: true,
      count: posts.length,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      data: posts
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get single blog post
// @route   GET /api/blogs/:slug
// @access  Public
const getPost = async (req, res, next) => {
  try {
    const post = await BlogPost.findOne({ slug: req.params.slug, status: 'published' })
      .populate('author', 'name avatar bio website socialMedia')
      .populate('category', 'name slug color')
      .populate({
        path: 'comments',
        match: { status: 'approved' },
        populate: {
          path: 'replies',
          match: { status: 'approved' }
        }
      });

    if (!post) {
      return res.status(404).json({
        success: false,
        error: 'Post not found'
      });
    }

    res.status(200).json({
      success: true,
      data: post
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Create blog post
// @route   POST /api/blogs
// @access  Private/Admin
const createPost = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    // Add author to req.body
    req.body.author = req.user.id;

    const post = await BlogPost.create(req.body);

    // Update category post count
    await Category.findByIdAndUpdate(
      post.category,
      { $inc: { postCount: 1 } }
    );

    const populatedPost = await BlogPost.findById(post._id)
      .populate('author', 'name avatar')
      .populate('category', 'name slug color');

    res.status(201).json({
      success: true,
      data: populatedPost
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update blog post
// @route   PUT /api/blogs/:id
// @access  Private/Admin
const updatePost = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    let post = await BlogPost.findById(req.params.id);

    if (!post) {
      return res.status(404).json({
        success: false,
        error: 'Post not found'
      });
    }

    const oldCategory = post.category;
    post = await BlogPost.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true
    }).populate('author', 'name avatar')
      .populate('category', 'name slug color');

    // Update category post counts if category changed
    if (req.body.category && oldCategory.toString() !== req.body.category) {
      await Category.findByIdAndUpdate(oldCategory, { $inc: { postCount: -1 } });
      await Category.findByIdAndUpdate(req.body.category, { $inc: { postCount: 1 } });
    }

    res.status(200).json({
      success: true,
      data: post
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Delete blog post
// @route   DELETE /api/blogs/:id
// @access  Private/Admin
const deletePost = async (req, res, next) => {
  try {
    const post = await BlogPost.findById(req.params.id);

    if (!post) {
      return res.status(404).json({
        success: false,
        error: 'Post not found'
      });
    }

    // Update category post count
    await Category.findByIdAndUpdate(
      post.category,
      { $inc: { postCount: -1 } }
    );

    await post.deleteOne();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get featured posts
// @route   GET /api/blogs/featured
// @access  Public
const getFeaturedPosts = async (req, res, next) => {
  try {
    const limit = parseInt(req.query.limit) || 5;

    const posts = await BlogPost.find({ status: 'published', featured: true })
      .populate('author', 'name avatar')
      .populate('category', 'name slug color')
      .sort('-publishedAt')
      .limit(limit);

    res.status(200).json({
      success: true,
      count: posts.length,
      data: posts
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get posts by category
// @route   GET /api/blogs/category/:categorySlug
// @access  Public
const getPostsByCategory = async (req, res, next) => {
  try {
    const category = await Category.findOne({ slug: req.params.categorySlug });
    
    if (!category) {
      return res.status(404).json({
        success: false,
        error: 'Category not found'
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const posts = await BlogPost.find({ 
      category: category._id, 
      status: 'published' 
    })
      .populate('author', 'name avatar')
      .populate('category', 'name slug color')
      .sort('-publishedAt')
      .skip(skip)
      .limit(limit);

    const total = await BlogPost.countDocuments({ 
      category: category._id, 
      status: 'published' 
    });

    res.status(200).json({
      success: true,
      category,
      count: posts.length,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      data: posts
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get posts by tag
// @route   GET /api/blogs/tag/:tag
// @access  Public
const getPostsByTag = async (req, res, next) => {
  try {
    const tag = req.params.tag.toLowerCase();
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const posts = await BlogPost.find({ 
      tags: tag, 
      status: 'published' 
    })
      .populate('author', 'name avatar')
      .populate('category', 'name slug color')
      .sort('-publishedAt')
      .skip(skip)
      .limit(limit);

    const total = await BlogPost.countDocuments({ 
      tags: tag, 
      status: 'published' 
    });

    res.status(200).json({
      success: true,
      tag,
      count: posts.length,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      data: posts
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Search posts
// @route   GET /api/blogs/search
// @access  Public
const searchPosts = async (req, res, next) => {
  try {
    const query = req.query.q;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    if (!query) {
      return res.status(400).json({
        success: false,
        error: 'Search query is required'
      });
    }

    const posts = await BlogPost.find({
      $and: [
        { status: 'published' },
        {
          $text: { $search: query }
        }
      ]
    })
      .populate('author', 'name avatar')
      .populate('category', 'name slug color')
      .sort({ score: { $meta: 'textScore' } })
      .skip(skip)
      .limit(limit);

    const total = await BlogPost.countDocuments({
      $and: [
        { status: 'published' },
        { $text: { $search: query } }
      ]
    });

    res.status(200).json({
      success: true,
      query,
      count: posts.length,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      data: posts
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Like/Unlike post
// @route   POST /api/blogs/:id/like
// @access  Private
const likePost = async (req, res, next) => {
  try {
    const post = await BlogPost.findById(req.params.id);

    if (!post) {
      return res.status(404).json({
        success: false,
        error: 'Post not found'
      });
    }

    const existingLike = post.likes.find(
      like => like.user.toString() === req.user.id
    );

    if (existingLike) {
      // Unlike
      post.likes = post.likes.filter(
        like => like.user.toString() !== req.user.id
      );
    } else {
      // Like
      post.likes.push({ user: req.user.id });
    }

    await post.save();

    res.status(200).json({
      success: true,
      liked: !existingLike,
      likeCount: post.likes.length
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Increment post views
// @route   PUT /api/blogs/:id/views
// @access  Public
const incrementViews = async (req, res, next) => {
  try {
    await BlogPost.findByIdAndUpdate(
      req.params.id,
      { $inc: { views: 1 } }
    );

    res.status(200).json({
      success: true
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getAllPosts,
  getPost,
  createPost,
  updatePost,
  deletePost,
  getFeaturedPosts,
  getPostsByCategory,
  getPostsByTag,
  searchPosts,
  likePost,
  incrementViews
};
