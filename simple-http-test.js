const http = require('http');
const url = require('url');

console.log('🚀 Creating Wilderness Trails Integrated Server...');

// Mock data for the camping blog
const mockData = {
  categories: [
    { _id: '1', name: 'Gear Reviews', slug: 'gear-reviews', color: '#349e63', postCount: 12 },
    { _id: '2', name: 'Trail Guides', slug: 'trail-guides', color: '#d9844a', postCount: 8 },
    { _id: '3', name: 'Wilderness Skills', slug: 'wilderness-skills', color: '#a08862', postCount: 15 },
    { _id: '4', name: 'Trip Reports', slug: 'trip-reports', color: '#6b6b68', postCount: 6 }
  ],
  blogPosts: [
    {
      _id: '1',
      title: 'Essential Backpacking Gear for Beginners',
      slug: 'essential-backpacking-gear-beginners',
      excerpt: 'A comprehensive guide to the must-have gear for your first backpacking adventure.',
      author: { name: '<PERSON>' },
      category: { name: 'Gear Reviews', color: '#349e63' },
      publishedAt: '2024-12-15T10:00:00Z',
      featured: true,
      views: 1247,
      likes: 89
    },
    {
      _id: '2',
      title: 'Top 10 Wilderness Trails in the Pacific Northwest',
      slug: 'top-10-wilderness-trails-pacific-northwest',
      excerpt: 'Discover the most breathtaking wilderness trails in Washington and Oregon.',
      author: { name: 'Mike Chen' },
      category: { name: 'Trail Guides', color: '#d9844a' },
      publishedAt: '2024-12-10T14:30:00Z',
      featured: true,
      views: 2156,
      likes: 156
    }
  ]
};

const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  console.log(`📡 ${method} ${path}`);

  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  // API Routes
  if (path === '/api/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: true,
      message: 'Wilderness Trails API is running!',
      timestamp: new Date().toISOString()
    }));
    return;
  }

  if (path === '/api/categories') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: true,
      count: mockData.categories.length,
      data: mockData.categories
    }));
    return;
  }

  if (path === '/api/blogs') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: true,
      count: mockData.blogPosts.length,
      data: mockData.blogPosts
    }));
    return;
  }

  if (path === '/api/blogs/featured') {
    const featured = mockData.blogPosts.filter(post => post.featured);
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: true,
      count: featured.length,
      data: featured
    }));
    return;
  }

  // Frontend Routes
  if (path === '/' || path === '/index.html') {
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(`
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wilderness Trails - Backpacking & Wilderness Camping Blog</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #349e63 0%, #26804f 100%);
            color: #333;
            line-height: 1.6;
        }
        .header {
            background: rgba(255,255,255,0.95);
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
        .nav { display: flex; justify-content: space-between; align-items: center; }
        .logo { font-size: 1.8rem; font-weight: bold; color: #349e63; }
        .nav-links { display: flex; list-style: none; gap: 2rem; }
        .nav-links a { text-decoration: none; color: #333; font-weight: 500; }
        .hero {
            background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 600"><rect fill="%23349e63" width="1200" height="600"/><path fill="%23ffffff" opacity="0.1" d="M0,300 Q300,200 600,300 T1200,300 L1200,600 L0,600 Z"/></svg>');
            background-size: cover;
            color: white;
            text-align: center;
            padding: 4rem 0;
        }
        .hero h1 { font-size: 3rem; margin-bottom: 1rem; text-shadow: 2px 2px 4px rgba(0,0,0,0.5); }
        .hero p { font-size: 1.2rem; margin-bottom: 2rem; opacity: 0.9; }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #d9844a;
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        .btn:hover { background: #cb6d3f; transform: translateY(-2px); }
        .main { background: white; padding: 3rem 0; }
        .section { margin-bottom: 3rem; }
        .section h2 { color: #349e63; margin-bottom: 1.5rem; font-size: 2rem; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; }
        .card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s ease;
        }
        .card:hover { transform: translateY(-5px); }
        .card-header { padding: 1.5rem; border-bottom: 1px solid #eee; }
        .card-body { padding: 1.5rem; }
        .category-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            color: white;
            margin-bottom: 0.5rem;
        }
        .meta { color: #666; font-size: 0.9rem; margin-top: 1rem; }
        .status {
            background: rgba(52, 158, 99, 0.1);
            border: 1px solid #349e63;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        .api-test { margin-top: 2rem; }
        .loading { color: #666; font-style: italic; }
        .error { color: #e74c3c; }
        .success { color: #27ae60; }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <nav class="nav">
                <div class="logo">🏔️ Wilderness Trails</div>
                <ul class="nav-links">
                    <li><a href="#home">Home</a></li>
                    <li><a href="#blog">Blog</a></li>
                    <li><a href="#categories">Categories</a></li>
                    <li><a href="#about">About</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <section class="hero">
        <div class="container">
            <h1>Wilderness Trails</h1>
            <p>Your ultimate guide to backpacking and wilderness camping adventures</p>
            <button class="btn" onclick="loadFeaturedPosts()">Explore Adventures</button>
        </div>
    </section>

    <main class="main">
        <div class="container">
            <div class="status">
                <h3>🎉 Application Status</h3>
                <p><strong>✅ Frontend:</strong> Running on http://localhost:8080</p>
                <p><strong>✅ Backend API:</strong> Integrated with frontend</p>
                <p><strong>✅ Database:</strong> Mock data loaded</p>
            </div>

            <section class="section">
                <h2>Featured Adventures</h2>
                <div id="featured-posts" class="grid">
                    <div class="loading">Loading featured posts...</div>
                </div>
            </section>

            <section class="section">
                <h2>Categories</h2>
                <div id="categories" class="grid">
                    <div class="loading">Loading categories...</div>
                </div>
            </section>

            <section class="section api-test">
                <h2>API Testing</h2>
                <button class="btn" onclick="testAPI()">Test Backend API</button>
                <div id="api-results"></div>
            </section>
        </div>
    </main>

    <script>
        // Load featured posts on page load
        async function loadFeaturedPosts() {
            try {
                const response = await fetch('/api/blogs/featured');
                const data = await response.json();

                const container = document.getElementById('featured-posts');
                if (data.success && data.data.length > 0) {
                    container.innerHTML = data.data.map(post => \`
                        <div class="card">
                            <div class="card-header">
                                <div class="category-badge" style="background-color: \${post.category.color}">
                                    \${post.category.name}
                                </div>
                                <h3>\${post.title}</h3>
                            </div>
                            <div class="card-body">
                                <p>\${post.excerpt}</p>
                                <div class="meta">
                                    By \${post.author.name} • \${new Date(post.publishedAt).toLocaleDateString()} • \${post.views} views
                                </div>
                            </div>
                        </div>
                    \`).join('');
                } else {
                    container.innerHTML = '<p>No featured posts available.</p>';
                }
            } catch (error) {
                document.getElementById('featured-posts').innerHTML = '<p class="error">Error loading featured posts.</p>';
            }
        }

        // Load categories
        async function loadCategories() {
            try {
                const response = await fetch('/api/categories');
                const data = await response.json();

                const container = document.getElementById('categories');
                if (data.success && data.data.length > 0) {
                    container.innerHTML = data.data.map(category => \`
                        <div class="card">
                            <div class="card-body">
                                <h3 style="color: \${category.color}">\${category.name}</h3>
                                <p>\${category.postCount} posts</p>
                            </div>
                        </div>
                    \`).join('');
                } else {
                    container.innerHTML = '<p>No categories available.</p>';
                }
            } catch (error) {
                document.getElementById('categories').innerHTML = '<p class="error">Error loading categories.</p>';
            }
        }

        // Test API endpoints
        async function testAPI() {
            const results = document.getElementById('api-results');
            results.innerHTML = '<div class="loading">Testing API endpoints...</div>';

            const tests = [
                { name: 'Health Check', url: '/api/health' },
                { name: 'Categories', url: '/api/categories' },
                { name: 'Blog Posts', url: '/api/blogs' },
                { name: 'Featured Posts', url: '/api/blogs/featured' }
            ];

            let html = '<h3>API Test Results:</h3>';

            for (const test of tests) {
                try {
                    const response = await fetch(test.url);
                    const data = await response.json();
                    html += \`<p class="success">✅ \${test.name}: \${response.status} - \${data.success ? 'Success' : 'Failed'}</p>\`;
                } catch (error) {
                    html += \`<p class="error">❌ \${test.name}: Failed - \${error.message}</p>\`;
                }
            }

            results.innerHTML = html;
        }

        // Load content on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadFeaturedPosts();
            loadCategories();
        });
    </script>
</body>
</html>
    `);
    return;
  }

  // 404 for unknown routes
  res.writeHead(404, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify({
    success: false,
    error: `Route ${path} not found`
  }));
});

const PORT = 8080;

server.listen(PORT, 'localhost', () => {
  console.log(`
🏕️  WILDERNESS TRAILS - INTEGRATED SERVER
✅ Server successfully started!
🌐 Frontend: http://localhost:${PORT}
📚 API Health: http://localhost:${PORT}/api/health
📝 API Blogs: http://localhost:${PORT}/api/blogs
🏷️  API Categories: http://localhost:${PORT}/api/categories
⭐ API Featured: http://localhost:${PORT}/api/blogs/featured
⏰ Started at: ${new Date().toISOString()}
  `);
});

server.on('error', (err) => {
  console.error('❌ Server error:', err);
});
