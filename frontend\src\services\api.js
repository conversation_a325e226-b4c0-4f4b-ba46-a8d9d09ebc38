import axios from 'axios'

// Create axios instance
const api = axios.create({
  baseURL: '/api',
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  getMe: () => api.get('/auth/me'),
  updateProfile: (userData) => api.put('/auth/profile', userData),
  changePassword: (passwordData) => api.put('/auth/change-password', passwordData),
  forgotPassword: (email) => api.post('/auth/forgot-password', { email }),
  resetPassword: (token, password) => api.put(`/auth/reset-password/${token}`, { password })
}

// Blog API
export const blogAPI = {
  getAllPosts: (params) => api.get('/blogs', { params }),
  getPost: (slug) => api.get(`/blogs/${slug}`),
  createPost: (postData) => api.post('/blogs', postData),
  updatePost: (id, postData) => api.put(`/blogs/${id}`, postData),
  deletePost: (id) => api.delete(`/blogs/${id}`),
  getFeaturedPosts: () => api.get('/blogs/featured'),
  getPostsByCategory: (categorySlug, params) => api.get(`/blogs/category/${categorySlug}`, { params }),
  getPostsByTag: (tag, params) => api.get(`/blogs/tag/${tag}`, { params }),
  searchPosts: (query, params) => api.get('/blogs/search', { params: { q: query, ...params } }),
  likePost: (id) => api.post(`/blogs/${id}/like`),
  incrementViews: (id) => api.put(`/blogs/${id}/views`)
}

// Category API
export const categoryAPI = {
  getAllCategories: () => api.get('/categories'),
  getCategory: (slug) => api.get(`/categories/${slug}`),
  createCategory: (categoryData) => api.post('/categories', categoryData),
  updateCategory: (id, categoryData) => api.put(`/categories/${id}`, categoryData),
  deleteCategory: (id) => api.delete(`/categories/${id}`)
}

// Comment API
export const commentAPI = {
  getCommentsByPost: (postId) => api.get(`/comments/post/${postId}`),
  createComment: (commentData) => api.post('/comments', commentData),
  updateComment: (id, commentData) => api.put(`/comments/${id}`, commentData),
  deleteComment: (id) => api.delete(`/comments/${id}`),
  approveComment: (id) => api.put(`/comments/${id}/approve`),
  rejectComment: (id) => api.put(`/comments/${id}/reject`),
  likeComment: (id) => api.post(`/comments/${id}/like`)
}

// Upload API
export const uploadAPI = {
  uploadImage: (formData) => api.post('/upload/image', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  }),
  deleteImage: (filename) => api.delete(`/upload/image/${filename}`)
}

export default api
