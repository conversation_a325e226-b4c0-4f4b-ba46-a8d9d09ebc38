const http = require('http');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting Wilderness Trails Frontend Server...');

const server = http.createServer((req, res) => {
  console.log(`📡 Request: ${req.method} ${req.url}`);
  
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  
  if (req.url === '/' || req.url === '/index.html') {
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(`
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wilderness Trails - Camping Blog</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #349e63 0%, #26804f 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            text-align: center;
            max-width: 800px;
            padding: 40px;
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        h1 { font-size: 3rem; margin-bottom: 20px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .subtitle { font-size: 1.2rem; margin-bottom: 30px; opacity: 0.9; }
        .status { 
            background: rgba(255,255,255,0.2); 
            padding: 20px; 
            border-radius: 10px; 
            margin: 20px 0;
            border-left: 4px solid #fff;
        }
        .success { border-left-color: #4CAF50; }
        .info { border-left-color: #2196F3; }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: rgba(255,255,255,0.2);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            margin: 10px;
            transition: all 0.3s ease;
            border: 2px solid rgba(255,255,255,0.3);
        }
        .btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        .mountain { font-size: 4rem; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="mountain">🏔️</div>
        <h1>Wilderness Trails</h1>
        <p class="subtitle">Your Guide to Backpacking & Wilderness Camping</p>
        
        <div class="status success">
            ✅ Frontend Server is Running Successfully!
        </div>
        
        <div class="status info">
            📍 Server: http://localhost:5175<br>
            🕒 Started: ${new Date().toLocaleString()}<br>
            🌐 Status: Operational
        </div>
        
        <div style="margin-top: 30px;">
            <a href="#" class="btn" onclick="testBackend()">🔗 Test Backend Connection</a>
            <a href="#" class="btn" onclick="showFeatures()">📋 View Features</a>
        </div>
        
        <div id="result" style="margin-top: 20px;"></div>
    </div>
    
    <script>
        function testBackend() {
            const result = document.getElementById('result');
            result.innerHTML = '<div class="status info">🔄 Testing backend connection...</div>';
            
            fetch('http://localhost:5000/api/health')
                .then(response => response.json())
                .then(data => {
                    result.innerHTML = '<div class="status success">✅ Backend connection successful!</div>';
                })
                .catch(error => {
                    result.innerHTML = '<div class="status" style="border-left-color: #f44336;">❌ Backend connection failed. Please start the backend server.</div>';
                });
        }
        
        function showFeatures() {
            const result = document.getElementById('result');
            result.innerHTML = \`
                <div class="status info">
                    <h3>🏕️ Wilderness Trails Features</h3>
                    <ul style="text-align: left; margin-top: 15px;">
                        <li>📝 Blog post management</li>
                        <li>🏷️ Category organization</li>
                        <li>💬 Comment system</li>
                        <li>🔍 Search functionality</li>
                        <li>📱 Responsive design</li>
                        <li>🎨 Nature-inspired theme</li>
                    </ul>
                </div>
            \`;
        }
        
        // Auto-test backend on load
        setTimeout(testBackend, 1000);
    </script>
</body>
</html>
    `);
  } else if (req.url === '/api/status') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: true,
      message: 'Frontend server is running',
      timestamp: new Date().toISOString(),
      port: 5175
    }));
  } else {
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Not Found');
  }
});

const PORT = 5175;

server.listen(PORT, 'localhost', () => {
  console.log(\`
🏕️  WILDERNESS TRAILS FRONTEND SERVER
✅ Server successfully started!
🌐 URL: http://localhost:\${PORT}
📱 Frontend: http://localhost:\${PORT}/
🔧 Status: http://localhost:\${PORT}/api/status
⏰ Started at: \${new Date().toISOString()}
  \`);
});

server.on('error', (err) => {
  console.error('❌ Frontend server error:', err);
});
